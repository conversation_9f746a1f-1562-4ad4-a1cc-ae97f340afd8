import React, { forwardRef } from "react";
import logo from "../models/rs_logo.jpg";

/**
 * PrintView - Reusable Print Component
 *
 * A unified print component that can handle different types of print layouts:
 * - Bill printing (POS receipts)
 * - Invoice printing (formal invoices)
 * - Voucher printing (payment/receive vouchers)
 *
 * Usage Examples:
 *
 * 1. Bill Printing:
 * <PrintView
 *   ref={printRef}
 *   printType="bill"
 *   products={products}
 *   totals={totals}
 *   customerInfo={customer}
 *   companyDetails={company}
 *   receivedAmount={paid}
 *   balanceAmount={balance}
 *   paymentType="cash"
 *   billNumber="BILL-001"
 *   formatCurrency={(amount) => `Rs. ${amount}`}
 * />
 *
 * 2. Invoice Printing:
 * <PrintView
 *   ref={printRef}
 *   printType="invoice"
 *   invoiceData={invoiceData}
 *   formatCurrency={(amount) => `Rs. ${amount}`}
 * />
 *
 * 3. Custom Template:
 * <PrintView
 *   ref={printRef}
 *   template={customTemplate}
 *   renderDynamicTemplate={renderFunction}
 *   products={products}
 *   totals={totals}
 * />
 */

const PrintView = forwardRef(({
  // Bill/Invoice data
  billData = {},
  products = [],
  totals = {},
  customerInfo = {},
  companyDetails = {},
  
  // Payment information
  receivedAmount = 0,
  balanceAmount = 0,
  paymentType = "cash",
  
  // Bill details
  billNumber = "",
  billTime = null,
  
  // Template and styling
  template = null,
  renderDynamicTemplate = null,
  
  // Utility functions
  formatCurrency = (amount) => `Rs. ${parseFloat(amount || 0).toFixed(2)}`,
  
  // Print type (bill, invoice, voucher)
  printType = "bill", // bill, invoice, voucher
  
  // Additional props for different print types
  voucherData = {},
  invoiceData = {},

  // Custom styling options
  showLogo = true,
  showTerms = true,
  showFooter = true,
  customStyles = {},
}, ref) => {
  
  // Get current user for cashier info
  const getCurrentUser = () => {
    try {
      const storedUser = localStorage.getItem("user") || sessionStorage.getItem("user");
      if (storedUser) {
        const user = JSON.parse(storedUser);
        return user.name || user.username || "Admin";
      }
      return "Admin";
    } catch {
      return "Admin";
    }
  };

  // Render Bill Print View
  const renderBillPrint = () => (
    <>
      {/* Bill Header */}
      {showLogo && (
        <div className="text-center bill-header" style={{ marginTop: "15px", padding: "15px 0" }}>
          <div
            className="company-name"
            style={{
              fontSize: "48px",
              fontWeight: "bolder",
              fontFamily: "'Times New Roman', 'Georgia', serif",
              color: "#1a1a1a",
              marginBottom: "12px",
              letterSpacing: "2px",
              textTransform: "uppercase",
              lineHeight: "1.2"
            }}
          >
            {companyDetails.company_name || companyDetails.business_name || "COMPANY NAME"}
            <div
              className="company-slogan"
              style={{ 
                fontSize: "14px",
                fontStyle: "italic",
                color: "#444",
               }}
            >
              MOTOR BIKE BORING, AUTO BORING, CONNECTING ROD FITTING
            </div>
            <div
              className="comapany-slogan"
              style={{ 
                fontSize: "10px",
                fontStyle: "italic",
                color: "#444",
               }}
            >
              ALL KIND OF MOTORCYCLE SPARE PARTS AVAILABLE HERE
            </div>
          </div>
          <div style={{
            fontSize: "10px",
            fontWeight: "bold",
            marginBottom: "6px",
            color: "#444",
            fontFamily: "'Arial', sans-serif"
          }}>
            {companyDetails.business_address}
          </div>
          <div style={{
            fontSize: "10px",
            marginBottom: "12px",
            color: "#444",
            fontFamily: "'Arial', sans-serif"
          }}>
            {companyDetails.contact_number}
          </div>
          <hr className="border-t border-black" style={{
            borderWidth: "2px",
            borderColor: "#333"
          }} />
        </div>
      )}

      {/* Bill Informatiion */}
      <div className="grid grid-cols-2 gap-2 text-sm bill-info">
        <div>
          <strong>Customer:</strong>{" "}
          <b>{customerInfo?.name || "Cash Customer"}</b>
        </div>
        <div>
          <strong>Bill No:</strong> <b>{billNumber}</b>
        </div>
        <div>
          <strong>Cashier:</strong> <b>{getCurrentUser()}</b>
        </div>
        <div>
          <strong>Date:</strong> <b>{new Date().toLocaleDateString()}</b>
        </div>
        <div>
          <strong>Payment:</strong> <b>{paymentType}</b>
        </div>
        <div>
          <strong>Time:</strong>
          <b>{" "}
          {billTime ||
            new Date().toLocaleTimeString("en-IN", {
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
              hour12: true,
            })}</b>
        </div>
      </div>

      {/* Products Table */}
      <table className="w-full mt-3 text-sm border-collapse bill-table" style={{ tableLayout: "fixed"}}>
        <thead>
          <tr className="bg-gray-100">
            <th className="px-1 py-1 text-center" style={{ width: "8%" }}>No</th>
            <th className="px-1 py-1 text-left" style={{ width: "25%" }}>Name</th>
            <th className="px-1 py-1 text-center" style={{ width: "12%" }}>Serial No</th>
            <th className="px-1 py-1 text-center" style={{ width: "8%" }}>QTY</th>
            <th className="px-1 py-1 text-right" style={{ width: "12%" }}>MRP</th>
            <th className="px-1 py-1 text-right" style={{ width: "10%" }}>Dis</th>
            <th className="px-1 py-1 text-right" style={{ width: "12%" }}>Price</th>
            <th className="px-1 py-1 text-right" style={{ width: "13%", paddingRight: "20px" }}>Total</th>
          </tr>
        </thead>
        <tbody>
          {products.map((product, index) => (
            <tr key={index}>
              <td
                className="px-1 py-1 text-center"
                style={{
                  fontWeight: "bold",
                  width: "8%",
                  verticalAlign: "top"
                }}
              >
                {index + 1}
              </td>
              <td
                className="px-1 py-1 text-left"
                style={{
                  fontWeight: "bold",
                  width: "25%",
                  wordWrap: "break-word",
                  overflowWrap: "break-word",
                  lineHeight: "1.2",
                  verticalAlign: "top"
                }}
              >
                {product.product_name}
              </td>
              <td
                className="px-1 py-1 text-center"
                style={{
                  fontWeight: "bold",
                  width: "12%",
                  verticalAlign: "top",
                  fontSize: "10px"
                }}
              >
                {product.batch_number || "-"}
              </td>
              <td
                className="px-1 py-1 text-center"
                style={{
                  fontWeight: "bold",
                  width: "8%",
                  verticalAlign: "top"
                }}
              >
                {product.qty || 1}
              </td>
              <td
                className="px-1 py-1 text-right"
                style={{
                  fontWeight: "bold",
                  width: "12%",
                  verticalAlign: "top"
                }}
              >
                {(product.mrp || 0).toFixed(2)}
              </td>
              <td
                className="px-1 py-1 text-right"
                style={{
                  fontWeight: "bold",
                  width: "10%",
                  verticalAlign: "top"
                }}
              >
                {(
                  (product.discount || 0) + (product.specialDiscount || 0)
                ).toFixed(2)}
              </td>
              <td
                className="px-1 py-1 text-right"
                style={{
                  fontWeight: "bold",
                  width: "12%",
                  verticalAlign: "top"
                }}
              >
                {(
                  (product.mrp || 0) - (product.discount || 0) / (product.qty || 1)
                ).toFixed(2)}
              </td>
              <td
                className="px-1 py-1 text-right"
                style={{
                  fontWeight: "bold",
                  width: "13%",
                  verticalAlign: "top",
                  paddingRight: "20px"
                }}
              >
                {(
                  ((product.mrp || 0) - (product.discount || 0) / (product.qty || 1)) *
                    (product.qty || 1) - (product.specialDiscount || 0)
                ).toFixed(2)}
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      <hr className="my-1 border-t border-black" />

      {/* Billing Summary */}
      <div
        style={{
          borderRadius: "4px",
          backgroundColor: "#F9FAFB",
          margin: "8px",
        }}
      >
        <h3
          style={{
            paddingBottom: "3px",
            fontSize: "18px",
            fontWeight: "600",
            color: "#1F2937",
          }}
        >
          Billing Summary
        </h3>
        <div
          style={{
            paddingTop: "2px",
            marginTop: "3px",
            borderTop: "1px solid #E5E7EB",
          }}
        >
          <p style={{ display: "flex", justifyContent: "space-between", marginBottom: "2px" }}>
            <strong style={{ textAlign: "left", color: "#1F2937" }}>Total count(No):</strong>
            <strong style={{ fontSize: "16px" }}>
              {products.length}
            </strong>
          </p>
          <p style={{ display: "flex", justifyContent: "space-between", marginBottom: "2px" }}>
            <strong style={{ textAlign: "left", color: "#1F2937" }}>Subtotal:</strong>
            <strong style={{ fontSize: "16px" }}>
              {formatCurrency(totals.subTotalMRP?.toFixed(2))}
            </strong>
          </p>
          <p style={{ display: "flex", justifyContent: "space-between", marginBottom: "2px" }}>
            <strong style={{ textAlign: "left", color: "#1F2937" }}>Total Discounts:</strong>
            <span style={{ fontSize: "14px" }}>
              {formatCurrency(totals.totalItemDiscounts)}
            </span>
          </p>
          {totals.totalBillDiscount > 0 && (
            <p style={{ display: "flex", justifyContent: "space-between", marginBottom: "2px" }}>
              <strong style={{ textAlign: "left", color: "#1F2937" }}>Bill Discount:</strong>
              <span style={{ fontSize: "14px" }}>
                {formatCurrency(totals.totalBillDiscount)}
              </span>
            </p>
          )}
          <p style={{ display: "flex", justifyContent: "space-between", marginBottom: "2px" }}>
            <strong style={{ textAlign: "left", color: "#1F2937" }}>Grand Total:</strong>
            <strong style={{ fontSize: "18px" }}>
              {formatCurrency(totals.finalTotal?.toFixed(2))}
            </strong>
          </p>
          <p style={{ display: "flex", justifyContent: "space-between", marginBottom: "2px" }}>
            <strong style={{ textAlign: "left", color: "#1F2937" }}>Paid:</strong>
            <span style={{ fontSize: "14px" }}>
              {formatCurrency(receivedAmount.toFixed(2))}
            </span>
          </p>
          <p style={{ display: "flex", justifyContent: "space-between", marginBottom: "0" }}>
            <strong style={{ textAlign: "left", color: "#1F2937" }}>Balance:</strong>
            <strong style={{ fontSize: "16px" }}>
              {formatCurrency(balanceAmount.toFixed(2))}
            </strong>
          </p>
        </div>
      </div>

      {/* Terms and Conditions */}
      {showTerms && (
        <div className="mt-2 text-xs text-left terms-conditions">
          <h4 className="font-bold text-center">Terms and Conditions</h4>
          <p className="ml-5 text-center">
            GOODS CAN BE RETURNS WITHIN 7 DAYS WITH ORG.CONDITIONS
          </p>
        </div>
      )}

      {/* Footer */}
      {showFooter && (
        <>
          <p className="mt-2 font-semibold text-center thanks">
            Thank You! Visit Again.
          </p>
          <p className="font-bold text-center text-[14px]">System by IMSS</p>
          <p className="text-[10px] font-bold text-center systemby-webs">visit🔗: www.imss.lk | 0752233855</p>
        </>
      )}
    </>
  );

  // Render Invoice Print View (placeholder for future implementation)
  const renderInvoicePrint = () => (
    <div className="text-center">
      <p>Invoice printing functionality will be implemented here</p>
    </div>
  );

  return (
    <div ref={ref} className="print-container">
      {template && renderDynamicTemplate ? (
        renderDynamicTemplate(template)
      ) : (
        <>
          {printType === "bill" && renderBillPrint()}
          {printType === "invoice" && renderInvoicePrint()}
        </>
      )}

      {/* Print Styles */}
      <style jsx global>{`
        @media print {
          @page {
            margin: 10mm 5mm;
            size: auto;
          }

          body {
            margin: 0;
            padding: 0;
            background: white;
            color: #000;
          }

          .print-container {
            width: 100%;
            margin: 0 8mm;
            padding: 0 5mm;
            box-sizing: border-box;
          }

          /* Header styles */
          .bill-header {
            margin: 10mm 0 8mm 0;
            padding: 8mm 0;
            text-align: center;
          }
          .bill-header img {
            max-width: 60mm;
            height: auto;
            margin: 0 auto 3mm auto;
          }
          .company-name {
            font-size: 22px !important;
            font-weight: bold !important;
            margin-bottom: 6mm !important;
            font-family: "Times New Roman", "Georgia", serif !important;
            letter-spacing: 1.5px !important;
            text-transform: uppercase !important;
            line-height: 1.2 !important;
            color: #1a1a1a !important;
          }
          .shop-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 2px;
            font-family: "Cressida", Elephant, cursive;
          }

          /* Table styles */
          .bill-table {
            width: 100%;
            border-collapse: collapse;
            margin: 3px 0;
            table-layout: fixed;
          }
          .bill-table th,
          .bill-table td {
            padding: 1px 2px;
            font-size: 11px;
            vertical-align: top;
            text-align: inherit;
            word-wrap: break-word;
            overflow-wrap: break-word;
          }
          .bill-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            border-bottom: 1px solid #ccc;
          }
          .bill-table td {
            border-bottom: 1px solid #eee;
          }
          /* Column width specifications for print */
          .bill-table th:nth-child(1),
          .bill-table td:nth-child(1) { width: 8%; text-align: center; }
          .bill-table th:nth-child(2),
          .bill-table td:nth-child(2) { width: 25%; text-align: left; }
          .bill-table th:nth-child(3),
          .bill-table td:nth-child(3) { width: 12%; text-align: center; font-size: 9px; }
          .bill-table th:nth-child(4),
          .bill-table td:nth-child(4) { width: 8%; text-align: center; }
          .bill-table th:nth-child(5),
          .bill-table td:nth-child(5) { width: 12%; text-align: right; }
          .bill-table th:nth-child(6),
          .bill-table td:nth-child(6) { width: 10%; text-align: right; }
          .bill-table th:nth-child(7),
          .bill-table td:nth-child(7) { width: 12%; text-align: right; }
          .bill-table th:nth-child(8),
          .bill-table td:nth-child(8) { width: 13%; text-align: right; }

          /* Info styles */
          .bill-info {
            margin: 0 5mm 0 5mm;
            padding: 0;
            font-size: 12px;
          }

          /* Terms and footer */
          .terms-conditions {
            margin-top: 5px;
            font-size: 10px;
          }
          .thanks {
            margin: 3px 0;
            font-size: 12px;
            text-align: center;
          }
          .systemby,
          .systemby-web {
            font-size: 8px;
            text-align: center;
            margin: 1px 0;
          }

          /* Invoice specific styles */
          .printable-invoice {
            width: 100%;
            margin: 0;
            padding: 10mm;
          }

          /* Hide non-print elements */
          .no-print {
            display: none !important;
          }

          /* Color adjustments for print */
          .bg-blue-600 { background-color: #2563eb !important; }
          .text-blue-600 { color: #2563eb !important; }
          .border-blue-600 { border-color: #2563eb !important; }
          .bg-gray-100 { background-color: #f3f4f6 !important; }
          .text-gray-700 { color: #374151 !important; }
          .border-gray-200 { border-color: #e5e7eb !important; }
        }
      `}</style>
    </div>
  );
});

PrintView.displayName = "PrintView";

export default PrintView;
